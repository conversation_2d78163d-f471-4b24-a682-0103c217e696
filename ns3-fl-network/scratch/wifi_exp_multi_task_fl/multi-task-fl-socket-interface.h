/* -*- Mode:C++; c-file-style:"gnu"; indent-tabs-mode:nil; -*- */
/*
 * Copyright (c) 2024 Multi-Task FL Team
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation;
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * ME<PERSON>HANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 * Author: Multi-Task FL Team
 */

#ifndef MULTI_TASK_FL_SOCKET_INTERFACE_H
#define MULTI_TASK_FL_SOCKET_INTERFACE_H

#include "ns3/command-line.h"
#include "ns3/config.h"
#include "ns3/double.h"
#include "ns3/string.h"
#include "ns3/log.h"
#include "ns3/yans-wifi-helper.h"
#include "ns3/mobility-helper.h"
#include "ns3/ipv4-address-helper.h"
#include "ns3/yans-wifi-channel.h"
#include "ns3/mobility-model.h"
#include "ns3/internet-stack-helper.h"
#include "ns3/flow-monitor-helper.h"
#include "ns3/flow-monitor-module.h"

#include <unistd.h>
#include <stdio.h>
#include <sys/socket.h>
#include <stdlib.h>
#include <netinet/in.h>
#include <string.h>
#include <map>
#include <memory>
#include <vector>

namespace ns3 {

    /**
     * \brief Multi-task client node information
     */
    class MultiTaskClientNode {
    public:
        MultiTaskClientNode(int clientId, double radius, double theta, int numTasks);
        
        int GetClientId() const { return m_clientId; }
        double GetRadius() const { return m_radius; }
        double GetTheta() const { return m_theta; }
        int GetNumTasks() const { return m_numTasks; }
        
    private:
        int m_clientId;
        double m_radius;
        double m_theta;
        int m_numTasks;
    };

    /**
     * \brief Task execution information for a specific client and task
     */
    struct TaskExecution {
        int clientId;
        int taskId;
        std::string taskName;
        double computeTime;      // T_ij - computed execution time from q_ij
        double priority;         // Task_ij - task priority [0,1]
        double bandwidth;        // B_ij - bandwidth allocation [0,1]
        double power;           // P_ij - power allocation [0,1]
        double fileSize;        // Fs_ij - file size to transmit (MB)
        double startTime;       // When this task should start
        double duration;        // How long this task should run
        bool isSelected;        // Whether this task is selected (select_ij >= 0.5)
    };

    /**
     * \brief Task simulation result for a specific execution
     */
    struct TaskResult {
        int clientId;
        int taskId;
        std::string taskName;
        double bandwidth;       // Actual bandwidth used (Mbps)
        double power;          // Actual power used (dBm)
        double packetLoss;     // Packet loss ratio
        double throughput;     // Achieved throughput (Mbps)
        double latency;        // Average latency (ms)
        double executionTime;  // Actual execution time (s)
    };

    /**
     * \ingroup multi-task-fl-socket-interface
     * \brief Socket interface for multi-task FL simulation
     */
    class MultiTaskFLSocketInterface {
    public:

        /**
         * \brief Command types for communication
         */
        enum class COMMAND : uint32_t {
            RESPONSE       = 0,
            RUN_SIMULATION = 1,
            EXIT           = 2,
            ENDSIM         = 3,
        };

        /**
         * \brief Task execution plan received from Python
         */
        struct TaskExecutionPlan {
            COMMAND command;
            uint32_t numClients;
            uint32_t numTasks;
            std::vector<TaskExecution> taskExecutions;  // All task executions in priority order
        };

        /**
         * \brief Simulation results for all tasks
         */
        struct SimulationResults {
            std::vector<TaskResult> taskResults;  // Results for each task execution
        };

        /**
         * \brief Constructor
         * \param port Listening port
         */
        MultiTaskFLSocketInterface(uint16_t port) : m_port(port) {}

        /**
         * \brief Wait for connection from Python
         */
        void WaitForConnection();

        /**
         * \brief Receive task execution plan from Python
         * \param clientNodes Map of client nodes
         * \return Task execution plan
         */
        TaskExecutionPlan ReceiveTaskExecutionPlan(
            std::map<int, std::shared_ptr<MultiTaskClientNode> > &clientNodes);

        /**
         * \brief Send simulation results back to Python
         * \param results Simulation results
         */
        void SendResults(const SimulationResults &results);

        /**
         * \brief Close socket connection
         */
        void Close();

    private:

        /**
         * \brief Receive resource allocation data for a client
         * \param clientId Client ID
         * \param numTasks Number of tasks
         * \return Resource allocation values
         */
        std::vector<double> ReceiveResourceAllocation(uint32_t clientId, uint32_t numTasks);

        /**
         * \brief Convert resource allocation to task executions
         * \param clientId Client ID
         * \param allocations Resource allocation values [select_ij, T_ij, B_ij, P_ij, Task_ij, Fs_ij] * numTasks
         * \param numTasks Number of tasks
         * \return Vector of task executions
         */
        std::vector<TaskExecution> ConvertToTaskExecutions(
            uint32_t clientId, const std::vector<double> &allocations, uint32_t numTasks);

        // Note: CalculateComputeTime and CalculateFileSize are no longer needed here
        // as T_ij and Fs_ij are calculated in Python

        /**
         * \brief Estimate WiFi transmission time based on file size and bandwidth
         * \param fileSize_MB File size in MB
         * \param bandwidth_allocation Bandwidth allocation [0,1]
         * \return Estimated transmission time in seconds
         */
        double EstimateTransmissionTime(double fileSize_MB, double bandwidth_allocation);

        /**
         * \brief Sort task executions by priority and create execution timeline
         * \param taskExecutions Vector of task executions
         * \return Sorted task executions with timing information
         */
        std::vector<TaskExecution> CreateExecutionTimeline(std::vector<TaskExecution> &taskExecutions);

        uint16_t m_port;              //!< Listening port number
        int m_server_fd;              //!< Listening socket file descriptor
        int m_new_socket;             //!< Session socket file descriptor
        struct sockaddr_in m_address; //!< Address data structure used to configure TCP socket.
    };
}

#endif
